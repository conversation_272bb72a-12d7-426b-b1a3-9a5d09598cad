<a href="#modal-download-doc" class="btn btn-outline-primary btn-modern-rounded me-1" data-bs-toggle="modal">
    <i class="fas fa-download me-1"></i>
    <span>Download Template</span>
</a>

<div class="modal fade" id="modal-download-doc">
    <div class="modal-dialog modal-xl">
        <div class="modal-content">
            <div class="modal-header border-0 pb-0">
                <h4 class="modal-title fw-semibold">Download Template</h4>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-hidden="true"></button>
            </div>
            <form action="{{ route('asset-management.asset-document.request_document') }}" method="post"
                id="form-download-doc">
                <div class="modal-body px-4">
                    @csrf
                    
                    <!-- Form Configuration Section -->
                    <div class="card border-0 shadow-sm mb-4">
                        <div class="card-header bg-transparent border-0 py-3 d-flex justify-content-between align-items-center">
                            <h6 class="mb-0 fw-semibold text-primary">
                                <i class="fas fa-cog me-2"></i>Konfigurasi Dokumen
                            </h6>
                            <button type="button" class="btn btn-sm btn-outline-info btn-modern-rounded" id="btn-help-download">
                                <i class="fas fa-question-circle me-1"></i>
                                <span>Bantuan</span>
                            </button>
                        </div>
                        <div class="card-body">
                            <div class="row g-3">
                                <div class="col-md-6">
                                    <label class="form-label fw-medium text-muted small">Template</label>
                                    <select name="template" id="template" class="form-select form-select-sm border-0 bg-light">
                                        <option disabled selected>Pilih Template</option>
                                        <option value="penempatan">BAST Penempatan</option>
                                        <option value="mutasi">BAST Mutasi</option>
                                        <option value="rusak">BAST Aset Rusak</option>
                                    </select>
                                </div>

                                <div class="col-md-6">
                                    <label for="target_ruangan" class="form-label fw-medium text-muted small">Target Ruangan</label>
                                    <select name="target_ruangan" id="target_ruangan" class="form-select form-select-sm border-0 bg-light target_ruangan"></select>
                                </div>

                                <div class="col-md-6">
                                    <label for="pihak_pertama" class="form-label fw-medium text-muted small">Pihak Pertama</label>
                                    <select name="pihak_pertama" id="pihak_pertama" class="form-select form-select-sm border-0 bg-light pihak_pertama"></select>
                                </div>

                                <div class="col-md-6">
                                    <label for="pihak_kedua" class="form-label fw-medium text-muted small">Pihak Kedua</label>
                                    <select name="pihak_kedua" id="pihak_kedua" class="form-select form-select-sm border-0 bg-light pihak_kedua"></select>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Asset List Section -->
                    <div class="card border-0 shadow-sm">
                        <div class="card-header bg-transparent border-0 py-3 d-flex justify-content-between align-items-center">
                            <h6 class="mb-0 fw-semibold text-primary">
                                <i class="fas fa-list me-2"></i>Daftar Asset
                            </h6>
                        </div>
                        <div class="card-body p-0">
                            <div class="table-responsive">
                                <table class="table table-hover mb-0 modern-table">
                                    <thead class="bg-light">
                                        <tr>
                                            <th class="border-0 py-3 px-4 fw-medium text-muted small">Kode Asset</th>
                                            <th class="border-0 py-3 px-4 fw-medium text-muted small">Kode QR</th>
                                            <th class="border-0 py-3 px-4 fw-medium text-muted small">Kode Register / SN</th>
                                            <th class="border-0 py-3 px-4 text-center fw-medium text-muted small" style="width: 60px;">#</th>
                                        </tr>
                                    </thead>
                                    <tbody id="table-download-doc" class="border-0">
                                        <!-- Empty state -->
                                        <tr id="empty-state" class="text-center">
                                            <td colspan="4" class="py-5 text-muted">
                                                <i class="fas fa-inbox fa-2x mb-3 d-block text-muted"></i>
                                                <p class="mb-0">Belum ada asset yang dipilih</p>
                                                <small>Klik "Tambah Asset" untuk menambahkan asset</small>
                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                        <div class="card-footer bg-transparent border-0 pt-3 pb-3">
                            <div class="d-flex">
                                <button type="button" class="btn btn-sm btn-outline-primary btn-modern-rounded" id="btn-add-row-download">
                                    <i class="fas fa-plus me-1"></i>
                                    <span>Tambah Asset</span>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer border-0 pt-0">
                    <button type="button" class="btn btn-outline-primary btn-modern-rounded me-1" data-bs-dismiss="modal">
                        <i class="fas fa-times me-1"></i>
                        <span>Batal</span>
                    </button>
                    <button type="submit" class="btn btn-primary btn-modern-rounded" id="btn-download-doc">
                        <i class="fas fa-download me-1"></i>
                        <span>Download Dokumen</span>
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Modal Bantuan -->
<div class="modal fade" id="modal-help-download" tabindex="-1" role="dialog" aria-labelledby="modalHelpDownloadLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header border-0 pb-0">
                <h5 class="modal-title fw-semibold" id="modalHelpDownloadLabel">
                    <i class="fas fa-question-circle me-2"></i>Panduan Konfigurasi Dokumen
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-hidden="true"></button>
            </div>
            <div class="modal-body px-4">
                <div class="help-section mb-4">
                    <h6 class="fw-semibold mb-3">Penjelasan Field</h6>
                    <div class="row g-3">
                        <div class="col-md-6">
                            <div class="help-item">
                                <strong>Template</strong>
                                <p class="text-muted small mb-0">Tipe dokumen yang akan dibuat</p>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="help-item">
                                <strong>Pihak Pertama</strong>
                                <p class="text-muted small mb-0">Pihak yang menyerahkan asset</p>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="help-item">
                                <strong>Target Ruangan</strong>
                                <p class="text-muted small mb-0">Ruangan penerima barang</p>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="help-item">
                                <strong>Pihak Kedua</strong>
                                <p class="text-muted small mb-0">Pihak penerima asset</p>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="help-section">
                    <h6 class="fw-semibold mb-3">Kondisi Khusus</h6>
                    <div class="help-item mb-3">
                        <strong>BAST Penempatan</strong>
                        <p class="text-muted small mb-0">
                            Pihak Pertama: Karyawan dengan tipe "PIC Pengurus Barang"
                        </p>
                    </div>
                    <div class="help-item">
                        <strong>BAST Aset Rusak</strong>
                        <div class="text-muted small">
                            <p class="mb-1">Pihak Kedua: Karyawan dengan tipe "PIC Barang Rusak"</p>
                            <p class="mb-0">Target Ruangan: Ruangan dengan tipe "Ruangan Aset Rusak"</p>
                        </div>
                    </div>
                </div>
            </div>
            <div class="modal-footer border-0 pt-0">
                <button type="button" class="btn btn-primary btn-modern-rounded" data-bs-dismiss="modal">
                    <i class="fas fa-check me-1"></i>
                    <span>Mengerti</span>
                </button>
            </div>
        </div>
    </div>
</div>

<style>
.modern-table {
    border: none;
}

.modern-table thead th {
    border: none;
    font-size: 12px;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    font-weight: 600;
}

.modern-table tbody td {
    border: none;
    padding: 12px 16px;
    vertical-align: middle;
}

.modern-table tbody tr:hover {
    background-color: #f8f9fa;
    transition: background-color 0.2s ease;
}

.modern-table tbody tr:not(:last-child) {
    border-bottom: 1px solid #f1f3f4;
}

.modern-table tbody tr.asset-row {
    transition: all 0.2s ease;
}

.form-select-sm {
    height: 38px;
    font-size: 14px;
    transition: all 0.2s ease;
}

.form-select-sm:focus {
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.15);
    border-color: #007bff;
    background-color: #fff !important;
}

.card {
    border-radius: 12px;
    transition: box-shadow 0.2s ease;
}

.card:hover {
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1) !important;
}

.card-header {
    border-radius: 12px 12px 0 0 !important;
}

.card-footer {
    border-radius: 0 0 12px 12px !important;
    background: #fafbfc;
}

.modal-content {
    border-radius: 16px;
    border: none;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
}

.modal-header {
    border-radius: 16px 16px 0 0;
}

.modal-footer {
    border-radius: 0 0 16px 16px;
}

#empty-state {
    background: #fafbfc;
}

#empty-state td {
    padding: 40px 20px;
}

.btn-outline-danger {
    border-color: #dc3545;
    color: #dc3545;
    background: transparent;
}

.btn-outline-danger:hover {
    background-color: #dc3545;
    border-color: #dc3545;
    color: #fff;
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(220, 53, 69, 0.3);
}

.text-primary {
    color: #007bff !important;
}

.fw-semibold {
    font-weight: 600 !important;
}

.fw-medium {
    font-weight: 500 !important;
}

.text-muted {
    color: #6c757d !important;
}

.small {
    font-size: 0.875em !important;
}

.bg-light {
    background-color: #f8f9fa !important;
}

.shadow-sm {
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075) !important;
}

.btn-outline-info {
    border-color: #6c757d;
    color: #6c757d;
    background: transparent;
}

.btn-outline-info:hover {
    background-color: #6c757d;
    border-color: #6c757d;
    color: #fff;
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(108, 117, 125, 0.3);
}

.help-section {
    padding: 20px;
    border-radius: 8px;
    background: #f8f9fa;
    border: 1px solid #e9ecef;
}

.help-item {
    padding: 12px 0;
    border-bottom: 1px solid #e9ecef;
}

.help-item:last-child {
    border-bottom: none;
}

.help-item strong {
    color: #495057;
    font-size: 14px;
    display: block;
    margin-bottom: 4px;
}

.help-item p {
    line-height: 1.4;
    margin-bottom: 0;
}

/* Modal blur effect */
.modal-blur {
    filter: blur(3px);
    opacity: 0.3;
    pointer-events: none;
    transition: all 0.3s ease;
}

/* Dark overlay for background modal */
.modal-blur::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.5);
    z-index: 1040;
    border-radius: 16px;
}

/* Ensure help modal appears above blurred modal */
#modal-help-download {
    z-index: 1060 !important;
}

/* Backdrop for help modal */
#modal-help-download + .modal-backdrop {
    z-index: 1055 !important;
}

/* Alert styling in modal */
.modal-body .alert {
    border-radius: 8px;
    border: none;
    margin-bottom: 20px;
    animation: slideInDown 0.3s ease-out;
}

.modal-body .alert-danger {
    background: linear-gradient(135deg, #f8d7da 0%, #f5c6cb 100%);
    color: #721c24;
    border-left: 4px solid #dc3545;
    box-shadow: 0 2px 8px rgba(220, 53, 69, 0.15);
}

.modal-body .alert-success {
    background: linear-gradient(135deg, #d4edda 0%, #c3e6cb 100%);
    color: #155724;
    border-left: 4px solid #28a745;
    box-shadow: 0 2px 8px rgba(40, 167, 69, 0.15);
}

.modal-body .alert .btn-close {
    opacity: 0.7;
    transition: opacity 0.2s;
}

.modal-body .alert .btn-close:hover {
    opacity: 1;
}

@keyframes slideInDown {
    from {
        transform: translateY(-20px);
        opacity: 0;
    }
    to {
        transform: translateY(0);
        opacity: 1;
    }
}

/* Error and success state styling */
.is-invalid {
    border-color: #dc3545 !important;
    box-shadow: 0 0 0 0.2rem rgba(220, 53, 69, 0.25) !important;
    animation: shake 0.5s ease-in-out;
}

.is-invalid:focus {
    border-color: #dc3545 !important;
    box-shadow: 0 0 0 0.2rem rgba(220, 53, 69, 0.25) !important;
    background-color: #fff !important;
}

.is-valid {
    border-color: #28a745 !important;
    box-shadow: 0 0 0 0.2rem rgba(40, 167, 69, 0.25) !important;
}

.is-valid:focus {
    border-color: #28a745 !important;
    box-shadow: 0 0 0 0.2rem rgba(40, 167, 69, 0.25) !important;
    background-color: #fff !important;
}

.border-danger {
    border: 2px solid #dc3545 !important;
    border-radius: 8px;
    animation: shake 0.5s ease-in-out;
}

@keyframes shake {
    0%, 100% { transform: translateX(0); }
    25% { transform: translateX(-5px); }
    75% { transform: translateX(5px); }
}
</style>