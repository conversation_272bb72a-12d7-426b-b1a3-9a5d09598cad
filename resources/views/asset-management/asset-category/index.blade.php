@extends('layouts.app')

@push('style')
<link href="{{ asset('/') }}plugins/datatables.net-bs4/css/dataTables.bootstrap4.min.css" rel="stylesheet" />
<link href="{{ asset('/') }}plugins/datatables.net-responsive-bs4/css/responsive.bootstrap4.min.css"
    rel="stylesheet" />
@endpush


@push('menu')
@include('menu.asset_management')
@endpush

@section('content')
<div class="panel panel-inverse">
    <div class="panel-heading">
        <h4 class="panel-title">{{ $title ?? 'Blank page' }}</h4>
    </div>
    <div class="panel-body">
        <div class="row d-flex mb-3">
            <div class="form-group col-md-4">
                <label for="filter" class="form-label"><i>Filter Data</i></label>
                <select name="filter" id="filter" class="form-select">
                    <option value="asset">Aset Rumah Sakit</option>
                    <option value="non-asset">Non Aset Rumah Sakit</option>
                    <option value="all">Semua <PERSON></option>
                </select>
            </div>
        </div>

        <div class="table-responsive">
            <table class="table table-bordered table-striped w-100" id="datatable">
                <thead>
                    <tr>
                        <th>#</th>
                        <th>Kode Kategori</th>
                        <th>Nama Kategori</th>
                        <th>Tipe Kategori</th>
                        <th>Sub Kategori</th>
                        <th>Jumlah Aset</th>
                        <th>Aksi</th>
                    </tr>
                </thead>
            </table>
        </div>
    </div>
</div>

<!-- Modal -->
<div class="modal fade" id="modal-dialog">
    <div class="modal-dialog modal-xl">
        <div class="modal-content">
            <div class="modal-header">
                <h4 class="modal-title">Detail Kategori</h4>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-hidden="true"></button>
            </div>
            <div class="modal-body">
                <div class="d-flex justify-content-between border-bottom mb-3 pb-2">
                    <div class="d-block fs-14px mb-2">
                        Kode Kategori : <strong id="kode_kategori">123</strong><br>
                        Nama Kategori : <strong id="nama_kategori">123</strong>
                    </div>

                    <div class="d-block fs-14px text-end">
                        Tipe Kategori : <strong id="tipe_kategori">123</strong><br>
                        Jumlah Aset : <strong id="jumlah_aset">10</strong>
                    </div>
                </div>

                <div class="row mb-3">
                    <div class="col-md-4">
                        <select class="form-select" id="filter_condition">
                            <option value="">Semua Kondisi</option>
                            <option value="BAIK">Baik</option>
                            <option value="RUSAK_RINGAN">Rusak Ringan</option>
                            <option value="RUSAK_BERAT">Rusak Berat</option>
                        </select>
                    </div>
                </div>

                <div class="table-responsive">
                    <table class="table table-striped w-100" id="datatable-asset">
                        <thead>
                            <tr>
                                <th>#</th>
                                <th>Kode QR</th>
                                <th>Kode Barang</th>
                                <th>Nama Aset</th>
                                <th>Kode Register / SN</th>
                                <th>Lokasi Aset</th>
                                <th>Kondisi</th>
                            </tr>
                        </thead>
                    </table>
                </div>
            </div>
            <div class="modal-footer">
                <a href="javascript:;" class="btn btn-white" data-bs-dismiss="modal">Close</a>
                <a href="javascript:;" class="btn btn-warning btn-export-excel" data-id=""><i class="fas fa-file-excel me-1"></i> Export Excel</a>
            </div>
        </div>
    </div>
</div>
@endsection

@push('script')
<script src="{{ asset('/') }}plugins/datatables.net/js/jquery.dataTables.min.js"></script>
<script src="{{ asset('/') }}plugins/datatables.net-bs4/js/dataTables.bootstrap4.min.js"></script>
<script src="{{ asset('/') }}plugins/datatables.net-responsive/js/dataTables.responsive.min.js"></script>
<script src="{{ asset('/') }}plugins/datatables.net-responsive-bs4/js/responsive.bootstrap4.min.js"></script>
<script src="{{ asset('/') }}plugins/sweetalert/dist/sweetalert.min.js"></script>
<script src="{{ asset('/js/app/asset-management/assetCategory.js') }}"></script>
@endpush