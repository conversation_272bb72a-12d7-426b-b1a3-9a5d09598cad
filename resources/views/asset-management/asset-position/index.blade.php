@extends('layouts.app')

@push('style')
<link href="{{ asset('/') }}plugins/datatables.net-bs4/css/dataTables.bootstrap4.min.css" rel="stylesheet" />
<link href="{{ asset('/') }}plugins/datatables.net-responsive-bs4/css/responsive.bootstrap4.min.css" rel="stylesheet" />
@endpush

@push('menu')
@include('menu.asset_management')
@endpush

@section('content')
<div class="panel panel-inverse">
    <div class="panel-heading">
        <h4 class="panel-title">{{ $title ?? 'Blank page' }}</h4>
    </div>
    <div class="panel-body">
        <div class="row d-flex justify-content-end mb-3">
            <div class="form-group col-md-4">
                <label for="category" class="form-label"><i><PERSON><PERSON><PERSON></i></label>
                <select name="category" id="category" class="form-select">
                    <option value="all">Se<PERSON><PERSON></option>
                    @foreach($roomCategories as $category)
                    <option value="{{ $category->id }}">{{ $category->room_category_name }}</option>
                    @endforeach
                </select>
            </div>
        </div>
        <div class="table-responsive">
            <table class="table table-bordered table-striped w-100" id="datatable">
                <thead>
                    <tr>
                        <th>#</th>
                        <th>Kode Ruangan</th>
                        <th>Nama Ruangan</th>
                        <th>Nama Gedung</th>
                        <th>Penanggung Jawab</th>
                        <th>Jumlah Aset</th>
                        <th>Aksi</th>
                    </tr>
                </thead>
            </table>
        </div>
    </div>
</div>

<div class="modal fade" id="modal-dialog">
    <div class="modal-dialog modal-xl">
        <div class="modal-content">
            <div class="modal-header">
                <h4 class="modal-title">Detail Kategori</h4>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-hidden="true"></button>
            </div>
            <div class="modal-body">
                <div class="d-flex justify-content-between border-bottom mb-3 pb-2">
                    <div class="d-block fs-14px mb-2">
                        Kode Ruangan : <strong id="kode_ruangan">123</strong><br>
                        Nama Ruangan : <strong id="nama_ruangan">123</strong>
                    </div>

                    <div class="d-block fs-14px text-end">
                        PIC Ruangan : <strong id="pic_room">123</strong><br>
                        Jumlah Aset : <strong id="jumlah_aset">10</strong>
                    </div>
                </div>

                <div class="table-responsive">
                    <table class="table table-striped w-100" id="datatable-asset">
                        <thead>
                            <tr>
                                <th>#</th>
                                <th>Kode QR</th>
                                <th>Kode Barang</th>
                                <th>Nama Aset</th>
                                <th>Kode Register / SN</th>
                                <th>Merk</th>
                                <th>Distributor</th>
                                <th>Harga</th>
                            </tr>
                        </thead>
                    </table>
                </div>
            </div>
            <div class="modal-footer">
                <a href="javascript:;" class="btn btn-white" data-bs-dismiss="modal">Close</a>
                <a href="javascript:;" class="btn btn-warning btn-export-excel" data-id=""><i class="fas fa-file-excel me-1"></i> Export Excel</a>

            </div>
        </div>
    </div>
</div>
@endsection

@push('script')
<script>
    let guard = @json($guard);
</script>
<script src="{{ asset('/') }}plugins/datatables.net/js/jquery.dataTables.min.js"></script>
<script src="{{ asset('/') }}plugins/datatables.net-bs4/js/dataTables.bootstrap4.min.js"></script>
<script src="{{ asset('/') }}plugins/datatables.net-responsive/js/dataTables.responsive.min.js"></script>
<script src="{{ asset('/') }}plugins/datatables.net-responsive-bs4/js/responsive.bootstrap4.min.js"></script>
<script src="{{ asset('/') }}plugins/sweetalert/dist/sweetalert.min.js"></script>
<script src="{{ asset('/js/app/asset-management/assetPosition.js') }}"></script>
@endpush
