@extends("layouts.app")
@push("style")
<link href="{{ asset('/') }}plugins/select2/dist/css/select2.min.css" rel="stylesheet" />
@endpush

@push("menu")
@include("menu.asset_management")
@endpush

@section("content")
<div class="panel panel-inverse">
    <div class="panel-heading">
        <h4 class="panel-title">{{ $title ?? "Blank page" }}</h4>
    </div>
    <div class="panel-body">
        <form action="" method="post" class="row" id="formdata">
            @csrf
            <input type="hidden" name="add" id="add" value="0">

            <div class="form-group col-md-4 mb-3">
                <label for="tanggal_barang_masuk" class="form-label">Tanggal Barang Masuk</label>
                <input type="date" name="tanggal_barang_masuk" id="tanggal_barang_masuk" class="form-control" value="{{ old('tanggal_barang_masuk') ?? now()->format('Y-m-d') }}">

                <span class="d-block text-danger" id="error_tanggal_barang_masuk"></span>
            </div>

            <div class="form-group col-md-4 mb-3">
                <label for="rekening_belanja" class="form-label">Uraian Rekening Belanja</label>
                <input type="text" name="rekening_belanja" id="rekening_belanja" class="form-control" value="{{ old('rekening_belanja') }}" placeholder="Enter Uraian Rekening Belanja">

                <span class="d-block text-danger" id="error_rekening_belanja"></span>
            </div>

            <div class="form-group col-md-4 mb-3">
                <label for="spending_account_code" class="form-label">Kode Rekening Belanja</label>
                <input type="text" name="spending_account_code" id="spending_account_code" class="form-control" placeholder="Masukkan Kode Rekening Belanja">
            </div>

            <div class="form-group col-md-4 mb-3">
                <label for="tanggal_pembayaran" class="form-label">Tanggal Pembayaran</label>
                <input type="date" name="tanggal_pembayaran" id="tanggal_pembayaran" class="form-control" value="{{ old('tanggal_pembayaran') ?? now()->format('Y-m-d') }}">

                <span class="d-block text-danger" id="error_tanggal_pembayaran"></span>
            </div>

            <div class="form-group col-md-4 mb-3">
                <label for="no_bast_kontrak" class="form-label">No. Kontrak/SP</label>
                <input type="text" name="no_bast_kontrak" id="no_bast_kontrak" class="form-control" value="{{ old('no_bast_kontrak') }}" placeholder="Enter No. Kontrak/SP">

                <span class="d-block text-danger" id="error_no_bast_kontrak"></span>
            </div>

            <div class="form-group col-md-4 mb-3">
                <label for="no_bast_pembayaran" class="form-label">No. BA Pembayaran</label>
                <input type="text" name="no_bast_pembayaran" id="no_bast_pembayaran" class="form-control" value="{{ old('no_bast_pembayaran') }}" placeholder="Enter No. BA Pembayaran">

                <span class="d-block text-danger" id="error_no_bast_pembayaran"></span>
            </div>

            <div class="form-group col-md-4 mb-3">
                <label for="asal_perolehan" class="form-label">Asal Perolehan</label>
                <select name="asal_perolehan" id="asal_perolehan" class="form-select asal-perolehan">
                    <option value="apbn">APBN</option>
                    <option value="apbd">APBD</option>
                    <option value="dak">DAK</option>
                    <option value="blud">BLUD</option>
                    <option value="hibah">Hibah</option>
                </select>

                <span class="d-block text-danger" id="error_asal_perolehan"></span>
            </div>

            <div class="form-group col-md-4 mb-3">
                <label for="distributor" class="form-label">Distributor</label>
                <select name="distributor" id="distributor" class="form-select distributor w-100">
                    <option value="">-- Pilih Distributor --</option>
                </select>

                <span class="d-block text-danger" id="error_distributor"></span>
            </div>

            <div class="form-group col-md-4 mb-3">
                <label for="kategori" class="form-label">Kategori</label>
                <select name="kategori" id="kategori" class="form-select kategori w-100">
                    <option value="">-- Pilih Kategori --</option>
                </select>

                <span class="d-block text-danger" id="error_kategori"></span>
            </div>

            <div class="form-group col-md-4 mb-3">
                <label for="kode_barang" class="form-label">Kode Barang</label>
                <select name="kode_barang" id="kode_barang" class="form-select kode-barang">
                    <option value="">-- Pilih Kode Barang --</option>
                </select>

                <span class="d-block text-danger" id="error_kode_barang"></span>
            </div>

            <div class="form-group col-md-4 mb-3">
                <label for="nama_barang" class="form-label">Nama Barang</label>
                <input type="text" name="nama_barang" id="nama_barang" class="form-control" value="{{ old('nama_barang') }}" placeholder="Enter nama barang">

                <span class="d-block text-danger" id="error_nama_barang"></span>
            </div>

            <div class="form-group col-md-4 mb-3">
                <label for="nama_umum" class="form-label">Nama Umum</label>
                <input type="text" name="nama_umum" id="nama_umum" class="form-control" value="{{ old('nama_umum') }}" placeholder="Enter nama umum">

                <span class="d-block text-danger" id="error_nama_umum"></span>
            </div>

            <div class="form-group col-md-4 mb-3">
                <label for="merk" class="form-label">Merk</label>
                <input type="text" name="merk" id="merk" class="form-control" value="{{ old('merk') }}" placeholder="Enter merk">

                <span class="d-block text-danger" id="error_merk"></span>
            </div>

            <div class="form-group col-md-4 mb-3">
                <label for="tipe" class="form-label">Tipe/Model</label>
                <input type="text" name="tipe" id="tipe" class="form-control" value="{{ old('tipe') }}" placeholder="Enter tipe">

                <span class="d-block text-danger" id="error_tipe"></span>
            </div>

            <div class="form-group col-md-4 mb-3">
                <label for="no_akd" class="form-label">No. AKD/AKL</label>
                <input type="text" name="no_akd" id="no_akd" class="form-control" value="{{ old('no_akd') }}" placeholder="Enter no. akd">

                <span class="d-block text-danger" id="error_no_akd"></span>
            </div>

            <div class="form-group col-md-4 mb-3">
                <label for="spesifikasi_umum" class="form-label">Spesifikasi Umum</label>
                <input type="text" name="spesifikasi_umum" id="spesifikasi_umum" class="form-control" value="{{ old('spesifikasi_umum') }}" placeholder="Enter spesifikasi umum">

                <span class="d-block text-danger" id="error_spesifikasi_umum"></span>
            </div>

            <div class="form-group col-md-4 mb-3">
                <label for="bahan" class="form-label">Bahan</label>
                <input type="text" name="bahan" id="bahan" class="form-control" value="{{ old('bahan') }}" placeholder="Enter bahan">

                <span class="d-block text-danger" id="error_bahan"></span>
            </div>

            <div class="form-group col-md-4 mb-3">
                <label for="ukuran" class="form-label">Ukuran</label>
                <input type="text" name="ukuran" id="ukuran" class="form-control" value="{{ old('ukuran') }}" placeholder="Enter ukuran">

                <span class="d-block text-danger" id="error_ukuran"></span>
            </div>

            <div class="form-group col-md-4 mb-3">
                <label for="satuan_barang" class="form-label">Satuan Barang</label>
                <select name="satuan_barang" id="satuan_barang" class="form-select satuan">
                    <option value="">-- Pilih Satuan Barang --</option>
                </select>

                <span class="d-block text-danger" id="error_satuan_barang"></span>
            </div>

            <div class="form-group col-md-4 mb-3">
                <label for="harga_satuan" class="form-label">Harga Satuan</label>
                <input type="text" name="harga_satuan" id="harga_satuan" class="form-control" value="{{ old('harga_satuan') }}" placeholder="Enter harga satuan">

                <span class="d-block text-danger" id="error_harga_satuan"></span>
            </div>

            <div class="form-group col-md-4 mb-3">
                <label for="jumlah_barang" class="form-label">Jumlah Barang</label>
                <input type="number" name="jumlah_barang" id="jumlah_barang" class="form-control" value="{{ old('jumlah_barang') }}" readonly placeholder="Enter jumlah barang">

                <span class="d-block text-danger" id="error_jumlah_barang"></span>
            </div>

            <div class="form-group col-md-4 mb-3">
                <label for="total_harga" class="form-label">Total Harga</label>
                <input type="text" name="total_harga" id="total_harga" class="form-control" value="{{ old('total_harga') }}" placeholder="Enter total harga" readonly>

                <span class="d-block text-danger" id="error_total_harga"></span>
            </div>

            {{-- <div class="form-group col-md-4 mb-5">
                <label for="kondisi" class="form-label">Kondisi</label>
                <select name="kondisi" id="kondisi" class="form-select">
                    <option value="">-- Pilih Kondisi --</option>
                    <option value="Baik">Baik</option>
                    <option value="Rusak Ringan">Rusak Ringan</option>
                    <option value="Rusak Berat ">Rusak Berat</option>
                </select>

                <span class="d-block text-danger" id="error_kondisi"></span>
            </div> --}}


            <div class="form-group col-md-4 mb-3">
                <label for="contract_form" class="form-label">Bentuk Kontrak</label>
                <input type="text" name="contract_form" id="contract_form" class="form-control" placeholder="Masukkan Bentuk Kontrak">

            </div>


            <div class="form-group col-md-4 mb-3">
                <label for="keterangan" class="form-label">Keterangan</label>
                <input type="text" name="keterangan" id="keterangan" class="form-control" value="{{ old('keterangan') }}" placeholder="Enter keterangan">
                <span class="d-block text-danger" id="error_keterangan"></span>
            </div>

            <div class="form-group col-md-4 mb-3">
                <label for="sub_activity_code" class="form-label text-dark">Kode Sub Kegiatan</label>
                <input type="text" name="sub_activity_code" id="sub_activity_code" class="form-control" placeholder="Masukkan Kode Sub Kegiatan">
                <span class="d-block text-danger" id="error_sub_activity_code"></span>
            </div>

            <div class="form-group col-md-4 mb-3">
                <label for="sub_activity_name" class="form-label text-dark">Nama Sub Kegiatan</label>
                <input type="text" name="sub_activity_name" id="sub_activity_name" class="form-control" placeholder="Masukkan Nama Sub Kegiatan">
                <span class="d-block text-danger" id="error_sub_activity_name"></span>
            </div>

            <div class="form-group col-md-4 mb-5">
                <label for="electricity" class="form-label text-dark">Kelistrikan</label>
                <input type="text" name="electricity" id="electricity" class="form-control" placeholder="Masukkan Kelistrikan">
                <span class="d-block text-danger" id="error_electricity"></span>
            </div>

            <div class="col-md-12">
                <div class="accordion" id="accordion">
                </div>
            </div>

            <div class="form-group col-md-12 mt-3">
                <button type="button" class="btn btn-primary" id="btn-save"><i class="fas fa-save me-1"></i> Simpan</button>
                <button type="button" class="btn btn-info" id="btn-save-add"><i class="fas fa-save me-1"></i> Simpan & Tambah</button>
                <a href="{{ route('asset-management.asset-hospital.index') }}" class="btn btn-outline-danger"><i class="fas fa-undo me-1"></i>Kembali</a>
            </div>
        </form>
    </div>
</div>
@endsection

@push("script")
<script src="{{ asset('/') }}plugins/select2/dist/js/select2.min.js"></script>
<script src="{{ asset('/js/app/asset-management/createAssetHospital.js') }}"></script>
<script>

</script>
@endpush