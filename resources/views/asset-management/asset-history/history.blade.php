<div class="row mb-3 d-flex justify-content-between border-bottom border-secondary border-2 pb-3">
    <h4>Data Aset</h4>
    <div class="col-md-2" id="qr-code"></div>
    <div class="col-md-5">
        <div class="d-block mb-1">Nama Aset : <strong id="asset_name">{{ $asset->item->item_name }}</strong></div>
        <div class="d-block mb-1">Kode Barang : <strong id="item_code">{{ $asset->item->item_code }}</strong></div>
        <div class="d-block mb-1">Harga : <strong id="unit_price"></strong>{{ number_format($asset->unit_price, 0, ",", ".") }}</div>
        <div class="d-block mb-1">Peruntukan : <strong id="location">{{ $asset->room->room_name ?? "-" }}</strong></div>
        <div class="d-block mb-1">Kode QR : <strong id="qr_code_asset">{{ $asset->qr_code }}</strong></div>
        <div class="d-block mb-1">Tag RFID : <strong id="tag_rfid">{{ $asset->rfid_tag }}</strong></div>
    </div>
    <div class="col-md-5">
        <div class="d-block mb-1">Kode Register : <strong id="register_code">{{ $asset->register_code }}</strong></div>
        <div class="d-block mb-1">Serial Number : <strong id="serial_num">{{ $asset->serial_number }}</strong></div>
        <div class="d-block mb-1">No. Rangka : <strong id="chassis_num">{{ $asset->chassis_number }}</strong></div>
        <div class="d-block mb-1">No. Mesin : <strong id="engine_num">{{ $asset->engine_number }}</strong></div>
        <div class="d-block mb-1">No. Polisi : <strong id="license_num">{{ $asset->license_plate_number }}</strong></div>
        <div class="d-block mb-1">No. BPKB : <strong id="bpkb_num">{{ $asset->bpkb_number }}</strong></div>
    </div>
</div>

<div class="row mb-3 d-flex justify-content-between border-bottom border-secondary pb-3">
    <h4>Document BAST</h4>
    <table class="table table-bordered table-striped">
        <thead>
            <tr>
                <th>No.</th>
                <th>Tanggal</th>
                <th>Jenis</th>
                <th>Pihak Pertama</th>
                <th>Pihak Kedua</th>
                <th>Ruangan</th>
                <th>#</th>
            </tr>
        </thead>

        <tbody>
            @foreach($documents as $document)
            <tr>
                <td>{{ $loop->iteration }}</td>
                <td>{{ $document->created_at }}</td>
                <td>{{ $document->document_type }}</td>
                <td>{{ $document->party1_name }}</td>
                <td>{{ $document->party2_name }}</td>
                <td>{{ $document->room->room_name ?? "-" }}</td>
                <td>
                    <a target="_blank" href="{{ asset("/storage/" . $document->document_path) }}">Link Dokumen</a>
                </td>
            </tr>
            @endforeach
        </tbody>
    </table>
</div>

<div class="row mb-3 d-flex justify-content-between border-bottom border-secondary pb-3">
    <h4>Jadwal Pemeliharaan</h4>
    <table class="table table-bordered table-striped">
        <thead>
            <tr>
                <th>No.</th>
                <th>Tanggal</th>
                <th>Jenis</th>
                <th>Kategori</th>
                <th>Note</th>
                <th>Ruangan</th>
            </tr>
        </thead>

        <tbody>
            @foreach($maintenanceSchedule as $mts)
            <tr>
                <td>{{ $loop->iteration }}</td>
                <td>{{ $mts->schedule_date }}</td>
                <td>{{ $mts->schedule_type }}</td>
                <td>{{ $mts->schedule_category }}</td>
                <td>{{ $mts->schedule_note }}</td>
                <td>{{ $mts->room_name  ?? "-" }}</td>
            </tr>
            @endforeach
        </tbody>
    </table>
</div>

<div class="row mb-3 d-flex justify-content-between border-bottom border-secondary pb-3">
    <h4>Riwayat Pemeliharaan</h4>
    <table class="table table-bordered table-striped">
        <thead>
            <tr>
                <th>No.</th>
                <th>Tanggal</th>
                <th>Kategori</th>
                <th>Jenis</th>
                <th>Total Pengeluaran (Rp.)</th>
                <th>Note</th>
                <th>Ruangan</th>
                <th>#</th>
            </tr>
        </thead>

        <tbody>
            @foreach($maintenanceActivity as $mtact)
            <tr>
                <td>{{ $loop->iteration }}</td>
                <td>{{ $mtact->activity_date }}</td>
                <td>{{ $mtact->activity_category }}</td>
                <td>{{ $mtact->activity_type }}</td>
                <td>{{ number_format($mtact->activity_cost, 0, ",", ".") }}</td>
                <td>{{ $mtact->notes }}</td>
                <td>{{ $mtact->room_name }}</td>
                <td>
                    <a href="{{ asset('/storage/' . $mtact->document_path) }}" target="_blank">Link Dokumen</a>
                </td>
            </tr>
            @endforeach
        </tbody>
    </table>
</div>

<div class="row mb-3 d-flex justify-content-between border-bottom border-secondary pb-3">
    <h4>Riwayat Perbaikan</h4>
    <table class="table table-bordered table-striped">
        <thead>
            <tr>
                <th>No.</th>
                <th>Tanggal</th>
                <th>Kerusakan</th>
                <th>Dokumen Kerusakan</th>
                <th>Tanggal Follow Up</th>
                <th>Notes</th>
                <th>Status</th>
                <th>Dokumen Follow Up</th>
            </tr>
        </thead>

        <tbody>
            @foreach($maintenanceIncidental as $incident)
            <tr>
                <td>{{ $loop->iteration }}</td>
                <td>{{ $incident->request_date }}</td>
                <td>{{ $incident->request_issue }}</td>
                <td><a href="{{ asset("/storage/". $incident->document_issue_path) }}" target="_blank">Link Dokumen</a></td>
                <td>{{ $incident->followup_date }}</td>
                <td>{{ $incident->followup_notes }}</td>
                <td>{{ $incident->followup_type }}</td>
                <td><a href="{{ asset("/storage/". $incident->document_followup_path) }}" target="_blank">Link Dokumen</a></td>
            </tr>
            @endforeach
        </tbody>
    </table>
</div>