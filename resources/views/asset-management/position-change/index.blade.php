@extends("layouts.app")

@push("style")
<link href="{{ asset('/') }}plugins/datatables.net-bs4/css/dataTables.bootstrap4.min.css" rel="stylesheet" />
<link href="{{ asset('/') }}plugins/datatables.net-responsive-bs4/css/responsive.bootstrap4.min.css" rel="stylesheet" />
<link href="{{ asset('/') }}plugins/select2/dist/css/select2.min.css" rel="stylesheet" />
<link href="{{ asset('/') }}plugins/bootstrap-daterangepicker/daterangepicker.css" rel="stylesheet" />
@endpush

@push("menu")
@include("menu.asset_management")
@endpush

@section("content")
<div class="panel panel-inverse">
    <div class="panel-heading">
        <h4 class="panel-title">{{ $title ?? "Blank page" }}</h4>
    </div>
    <div class="panel-body">
        <div class="row justify-content-end">
            <div class="col-md-1 btn-group">
                <a href="#" class="btn btn-secondary"><i class="fas fa-filter"></i></a>
                <a href="#" class="btn btn-secondary dropdown-toggle" data-bs-toggle="dropdown">
                    <i class="fa fa-caret-down"></i>
                </a>
                <ul class="dropdown-menu dropdown-menu-end">
                    <li>
                        <a href="javascript:;" class="dropdown-item" data-id="ruangan">Filter by Ruangan</a>
                        <a href="javascript:;" class="dropdown-item" data-id="asset">Filter by QR / Asset</a>
                    </li>
                </ul>
            </div>
        </div>

        <div class="row mb-3">
            <div class="form-group col-md-4" id="form-ruangan">
                <label for="ruangan" class="form-label"><i>Ruangan</i></label>
                <select name="ruangan" id="ruangan" class="form-select ruangan"></select>
            </div>

            <div class="form-group col-md-4 d-none" id="form-asset">
                <label for="asset" class="form-label"><i>QR / Asset</i></label>
                <select name="asset" id="asset" class="form-select asset"></select>
            </div>

            <div class="form-group col-md-4">
                <label class="form-label"><i>Periode</i></label>
                <input id="datepicker" type="text" class="form-control" readonly="readonly" style="background-color:white; cursor:pointer;">
            </div>

            <div class="form-group col-md-4">
                <button type="button" id="btn-filter" class="btn btn-primary mt-20px">Tampilkan Data</button>
            </div>
        </div>

        <div class="table-responsive">
            <table class="table table-bordered table-striped w-100" id="datatable">
                <thead>
                    <tr>
                        <th>#</th>
                        <th>Kode Aset</th>
                        <th>Nama Aset</th>
                        <th>Ruangan KIR</th>
                        <th>Asal Ruangan</th>
                        <th>Ruangan Terbaru</th>
                        <th>Waktu Perpindahan</th>
                    </tr>
                </thead>
            </table>
        </div>
    </div>
</div>
@endsection

@push("script")
<script src="{{ asset('/') }}plugins/datatables.net/js/jquery.dataTables.min.js"></script>
<script src="{{ asset('/') }}plugins/datatables.net-bs4/js/dataTables.bootstrap4.min.js"></script>
<script src="{{ asset('/') }}plugins/datatables.net-responsive/js/dataTables.responsive.min.js"></script>
<script src="{{ asset('/') }}plugins/datatables.net-responsive-bs4/js/responsive.bootstrap4.min.js"></script>
<script src="{{ asset('/') }}plugins/select2/dist/js/select2.min.js"></script>
<script src="{{ asset('/') }}plugins/moment/moment.js"></script>
<script src="{{ asset('/') }}plugins/bootstrap-daterangepicker/daterangepicker.js"></script>
<script src="{{ asset('/js/app/asset-management/positionChange.js') }}"></script>
@endpush
