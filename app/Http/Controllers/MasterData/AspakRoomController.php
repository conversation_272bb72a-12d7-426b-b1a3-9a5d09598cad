<?php

namespace App\Http\Controllers\MasterData;

use App\Http\Controllers\Controller;
use App\Models\AspakServiceRoom;
use Illuminate\Http\Request;

class AspakRoomController extends Controller
{
    public function index()
    {
        $title = "<PERSON>uangan";
        $breadcrumbs = ["Master Data", "Aspak", "Ruangan"];

        return view('master-data.aspak.room.index', compact('title', 'breadcrumbs'));
    }

    public function list(Request $request)
    {
        if (!$request->ajax()) {
            return response()->json([
                "data" => 'Not implemented'
            ], 200);
        }

        try {
            $queryBuilder = AspakServiceRoom::select('aspak_service_rooms.*');
            $getRooms = $queryBuilder->get();
        } catch (\Throwable $th) {
            return response()->json([
                "data" => [],
                "message" => $th->getMessage()
            ], 500);
        }

        return response()->json([
            "data" => $getRooms
        ]);

    }


    public function edit(AspakServiceRoom $aspakServiceRoom)
    {
        return response()->json([
            "data" => 'Not implemented'
        ], 200);
    }

    public function destroy(AspakServiceRoom $aspakServiceRoom)
    {
        return response()->json([
            "data" => 'Not implemented'
        ], 200);
    }

    public function store(Request $request)
    {
        return response()->json([
            "data" => 'Not implemented'
        ], 200);
    }

    public function create(Request $request)
    {
        return response()->json([
            "data" => 'Not implemented'
        ], 200);
    }

    public function update(Request $request, AspakServiceRoom $aspakServiceRoom)
    {
        return response()->json([
            "data" => 'Not implemented'
        ], 200);
    }
}
