<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\SoftDeletes;

class AspakServiceRoom extends Model
{
    use HasFactory, SoftDeletes;

    protected $table = 'aspak_service_rooms';

    protected $fillable = [
        'room_service_code',
        'room_service_name',
        'parent_id',
        'type',
        'created_by',
        'updated_by',
        'deleted_by',
        'created_by_name',
        'updated_by_name',
        'deleted_by_name'
    ];

    protected $casts = [
        'type' => 'string',
    ];

    /**
     * Relationship untuk parent (self-referencing)
     */
    public function parent(): BelongsTo
    {
        return $this->belongsTo(AspakServiceRoom::class, 'parent_id');
    }

    /**
     * Relationship untuk children (self-referencing)
     */
    public function children(): HasMany
    {
        return $this->hasMany(AspakServiceRoom::class, 'parent_id');
    }

    /**
     * Relationship untuk user yang membuat record
     */
    public function createdBy(): BelongsTo
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    /**
     * Relationship untuk user yang mengupdate record
     */
    public function updatedBy(): BelongsTo
    {
        return $this->belongsTo(User::class, 'updated_by');
    }

    /**
     * Relationship untuk user yang menghapus record
     */
    public function deletedBy(): BelongsTo
    {
        return $this->belongsTo(User::class, 'deleted_by');
    }

    /**
     * Scope untuk pencarian
     */
    public function scopeSearch($query)
    {
        if (request()->has('q')) {
            $query->where('room_service_name', 'like', '%' . request('q') . '%')
                ->orWhere('room_service_code', 'like', '%' . request('q') . '%');
        }
    }

    /**
     * Scope untuk filter berdasarkan type
     */
    public function scopeByType($query, $type)
    {
        return $query->where('type', $type);
    }

    /**
     * Scope untuk mendapatkan hanya group
     */
    public function scopeGroups($query)
    {
        return $query->where('type', 'GROUP');
    }

    /**
     * Scope untuk mendapatkan hanya room service
     */
    public function scopeRoomServices($query)
    {
        return $query->where('type', 'ROOM_SERVICE');
    }

    /**
     * Scope untuk mendapatkan root items (tanpa parent)
     */
    public function scopeRoots($query)
    {
        return $query->whereNull('parent_id');
    }
}
