$(document).ready(function () {
    let roomDataFromServer = [];
    let roomDataFromServerIndexed = new Map();

    let roomData = [];
    let roomDataNested = [];
    let roomDataNestedFiltered = [];

    let indexFlatten = 0;

    // Initialize DataTable
    const table = $('#table-data').DataTable({
        serverSide: true,
        processing: true,
        paging: false,
        searching: true,
        searchDelay: 500,
        ajax: function (payload, callback, settings) {
            console.log(payload);

            indexFlatten = 0;
            /*
                Load data Rooms.
                Example response:
                {
                    data: [
                        {
                            "id": 1,
                            "room_service_code": "GRP-L1-001",
                            "room_service_name": "Grup Administrasi Rutin",
                            "parent_id": null,
                            "type": "GROUP",
                            "created_at": "2025-08-22T17:33:20.000000Z",
                            "updated_at": "2025-08-22T17:33:20.000000Z",
                            "deleted_at": null,
                            "updated_by": 1,
                            "created_by": 1,
                            "deleted_by": null,
                            "created_by_name": "System",
                            "updated_by_name": "System",
                            "deleted_by_name": null
                        },
                        ...
                    ]
                }
            */
            
            // Function to process data and return filtered results
            function processAndFilterData() {
                // Filter Name
                if (payload.search.value) {
                    roomDataNestedFiltered = searchInPerNestedData(roomDataNested, payload.search.value);
                } else {
                    roomDataNestedFiltered = roomDataNested;
                }

                // Order by column
                roomDataNestedFiltered = orderByColumnPerNestedData(roomDataNestedFiltered, payload.order[0].column, payload.order[0].dir);

                // Get flattened data for display
                const flattenedData = flattenNestedData(roomDataNestedFiltered);
                
                callback({
                    draw: payload.draw,
                    data: flattenedData,
                    recordsTotal: roomData.length,
                    recordsFiltered: flattenedData.length,
                });
            }
            
            // Load data from server if not already loaded
            if (roomDataFromServer.length === 0) {
                $.ajax({
                    url: '/master-data/aspak/room/list',
                    type: 'GET',
                    success: function (response) {
                        const {data} = response;
                        wrapProcess(data);
                        processAndFilterData();
                    },
                    error: function(xhr, status, error) {
                        console.error('Error loading room data:', error);
                        callback({
                            draw: payload.draw,
                            data: [],
                            recordsTotal: 0,
                            recordsFiltered: 0,
                        });
                    }
                });
            } else {
                // Data already loaded, just process and filter
                processAndFilterData();
            }

        },
        columns: [
            {
                data: 'DT_RowIndex',
                name: 'DT_RowIndex',
                orderable: false,
                searchable: false,
                width: '5%',
                className: 'text-center',
                visible: false,
            },
            {
                data: 'room_service_name', name: 'room_service_name', width: '40%'
            },
            {
                data: 'room_service_code', name: 'room_service_code', width: '15%'
            },
            {
                data: 'level',
                name: 'level',
                orderable: false,
                searchable: false,
            },
            {
                data: 'parent_name',
                name: 'parent_name',
                orderable: false,
                searchable: false,
                width: '15%',
                className: 'text-center'
            },
            {
                data: null, name: 'action', orderable: false, searchable: false, width: '20%', className: 'text-center',
                render: function (data, type, row) {
                    return `
                    <button class="btn btn-sm btn-warning btn-edit" data-id="${row.id}"><i class="fas fa-edit"></i></button>
                `;
                }
            },
        ],
        order: [[1, 'asc']],
    });

    function determineLevel(item) {
        let level = 1;
        let parentId = item.parent_id;
        while (parentId) {
            level++;
            parentId = roomDataFromServerIndexed.get(parentId)?.parent_id;
        }
        return level;
    }

    function getChildren(parentId, data) {
        return data.filter(item => item.parent_id === parentId).map(item => {
            return {
                ...item,
                children: getChildren(item.id, data)
            };
        });
    }

    function preProcessNestedData(data) {
        const rootNodes = data.filter(item => !item.parent_id);
        return rootNodes.map(item => {
            return {
                ...item,
                children: getChildren(item.id, data)
            };
        });
    }

    function wrapProcess(data) {
        roomDataFromServer = data;

        roomDataFromServerIndexed.clear();
        roomDataFromServer.forEach(function (item) {
            roomDataFromServerIndexed.set(item.id, item);
        });

        roomDataFromServer.forEach(function (item, index) {
            roomData.push({
                ...item,
                DT_RowIndex: index,
                parent_name: roomDataFromServerIndexed.get(item.parent_id)?.room_service_name || null,
                level: determineLevel(item)
            });
        });
        roomDataNested = preProcessNestedData(roomData);
    }

    function searchInPerNestedData(dataNestedRooms, keyword) {
        keyword = keyword.toLowerCase();
        const results = []

        function match(items) {
            return items.room_service_name.toLowerCase().includes(keyword);
        }

        for (const nestedRoom of dataNestedRooms) {
            if (match(nestedRoom)) {
                results.push(nestedRoom);
            } else {
                if (nestedRoom.children) {
                    const childrenResults = searchInPerNestedData(nestedRoom.children, keyword);
                    if (childrenResults.length > 0) {
                        results.push({
                            ...nestedRoom,
                            children: childrenResults
                        });
                    }
                }
            }
        }

        return results;
    }

    function orderByColumnPerNestedData(dataNestedRooms, column, order) {
        let results = dataNestedRooms

        // Sorting current level
        results.sort((a, b) => {
            if (order === 'asc') {
                return a[column] > b[column] ? 1 : -1;
            } else {
                return a[column] < b[column] ? 1 : -1;
            }
        });

        results.forEach(function (item) {
            if (item.children) {
                item.children = orderByColumnPerNestedData(item.children, column, order);
            }
        });

        return results;
    }

    function flattenNestedData(dataNestedRooms) {
        let results = [];
        for (const item of dataNestedRooms) {
            indexFlatten++;
            item.DT_RowIndex = indexFlatten;
            results.push(item);
            if (item.children) {
                results = results.concat(flattenNestedData(item.children));
            }
        }
        return results;
    }

});
