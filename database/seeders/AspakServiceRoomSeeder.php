<?php

namespace Database\Seeders;

use App\Models\AspakServiceRoom;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class AspakServiceRoomSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Data arrays untuk membuat nama yang realistis
        $groupNames = ['<PERSON>ministrasi', '<PERSON><PERSON><PERSON>', 'Operasional', 'Tek<PERSON>', 'Medis', 'Farmasi', 'Laboratorium', 'Radiologi', 'Gizi', 'Keperawatan'];
        $serviceNames = ['Pelayanan', 'Konsultasi', 'P<PERSON>eriksaan', 'Tindak<PERSON>', 'Terapi', 'Monitoring', 'Eva<PERSON><PERSON>', 'Perawatan', 'Rehabilitasi', 'Edukasi'];
        $detailNames = ['Rutin', 'Khusus', 'Darurat', 'Komprehensif', 'Lanjutan', 'Dasar', 'Spesialis', '<PERSON>um', 'Intensif', '<PERSON>rka<PERSON>'];
        
        // Clear existing data
        AspakServiceRoom::truncate();
        
        $createdRecords = [];
        $totalRecords = 2000;
        
        // Level 1: Root Groups (folders) - sekitar 10-15 root groups
        $rootGroupsCount = 12;
        $level1Groups = [];
        
        for ($i = 1; $i <= $rootGroupsCount; $i++) {
            $record = AspakServiceRoom::create([
                'room_service_code' => 'GRP-L1-' . str_pad($i, 3, '0', STR_PAD_LEFT),
                'room_service_name' => 'Grup ' . $groupNames[($i - 1) % count($groupNames)] . ' ' . $detailNames[($i - 1) % count($detailNames)],
                'parent_id' => null,
                'type' => 'GROUP',
                'created_by' => 1,
                'updated_by' => 1,
                'created_by_name' => 'System',
                'updated_by_name' => 'System',
            ]);
            
            $level1Groups[] = $record;
            $createdRecords[] = $record;
        }
        
        // Level 2: Sub Groups dan beberapa Room Services
        $level2Groups = [];
        $level2Counter = 1;
        
        foreach ($level1Groups as $parentGroup) {
            // Setiap level 1 group memiliki 8-12 children (mix group dan room service)
            $childrenCount = 8 + ($parentGroup->id % 5); // 8-12
            $groupsInLevel2 = 3 + ($parentGroup->id % 3); // 3-5 // 3-6 groups, sisanya room services
            
            // Buat sub groups dulu
            for ($j = 1; $j <= $groupsInLevel2; $j++) {
                $record = AspakServiceRoom::create([
                    'room_service_code' => 'GRP-L2-' . str_pad($level2Counter, 3, '0', STR_PAD_LEFT),
                    'room_service_name' => 'Sub Grup ' . $serviceNames[($level2Counter - 1) % count($serviceNames)] . ' ' . $groupNames[($level2Counter - 1) % count($groupNames)],
                    'parent_id' => $parentGroup->id,
                    'type' => 'GROUP',
                    'created_by' => 1,
                    'updated_by' => 1,
                    'created_by_name' => 'System',
                    'updated_by_name' => 'System',
                ]);
                
                $level2Groups[] = $record;
                $createdRecords[] = $record;
                $level2Counter++;
            }
            
            // Buat room services di level 2
            $roomServicesInLevel2 = $childrenCount - $groupsInLevel2;
            for ($k = 1; $k <= $roomServicesInLevel2; $k++) {
                $record = AspakServiceRoom::create([
                    'room_service_code' => 'RS-L2-' . str_pad(count($createdRecords) + 1, 4, '0', STR_PAD_LEFT),
                    'room_service_name' => $serviceNames[($k - 1) % count($serviceNames)] . ' ' . $detailNames[($k - 1) % count($detailNames)] . ' Service',
                    'parent_id' => $parentGroup->id,
                    'type' => 'ROOM_SERVICE',
                    'created_by' => 1,
                    'updated_by' => 1,
                    'created_by_name' => 'System',
                    'updated_by_name' => 'System',
                ]);
                
                $createdRecords[] = $record;
            }
        }
        
        // Level 3: Mostly Room Services dengan beberapa sub groups
        foreach ($level2Groups as $parentGroup) {
            // Setiap level 2 group memiliki 15-25 children (mayoritas room services)
            $childrenCount = 15 + ($parentGroup->id % 11); // 15-25
            $groupsInLevel3 = $parentGroup->id % 3; // 0-2 // 0-2 groups, sisanya room services
            
            // Buat sub groups di level 3 (jika ada)
            for ($j = 1; $j <= $groupsInLevel3; $j++) {
                $record = AspakServiceRoom::create([
                    'room_service_code' => 'GRP-L3-' . str_pad(count($createdRecords) + 1, 4, '0', STR_PAD_LEFT),
                    'room_service_name' => 'Detail ' . $detailNames[($j - 1) % count($detailNames)] . ' ' . $groupNames[($j - 1) % count($groupNames)],
                    'parent_id' => $parentGroup->id,
                    'type' => 'GROUP',
                    'created_by' => 1,
                    'updated_by' => 1,
                    'created_by_name' => 'System',
                    'updated_by_name' => 'System',
                ]);
                
                $createdRecords[] = $record;
            }
            
            // Buat room services di level 3
            $roomServicesInLevel3 = $childrenCount - $groupsInLevel3;
            for ($k = 1; $k <= $roomServicesInLevel3; $k++) {
                $record = AspakServiceRoom::create([
                    'room_service_code' => 'RS-L3-' . str_pad(count($createdRecords) + 1, 4, '0', STR_PAD_LEFT),
                    'room_service_name' => $serviceNames[($k - 1) % count($serviceNames)] . ' ' . $detailNames[($k - 1) % count($detailNames)] . ' Detail Service',
                    'parent_id' => $parentGroup->id,
                    'type' => 'ROOM_SERVICE',
                    'created_by' => 1,
                    'updated_by' => 1,
                    'created_by_name' => 'System',
                    'updated_by_name' => 'System',
                ]);
                
                $createdRecords[] = $record;
                
                // Stop jika sudah mencapai target 2000 records
                if (count($createdRecords) >= $totalRecords) {
                    break 2;
                }
            }
        }
        
        // Jika masih kurang dari 2000, tambahkan room services acak
        while (count($createdRecords) < $totalRecords) {
            // Pilih parent secara acak dari level 1 atau level 2 groups
            $allGroups = array_merge($level1Groups, $level2Groups);
            $randomParent = $allGroups[count($createdRecords) % count($allGroups)];
            
            $record = AspakServiceRoom::create([
                'room_service_code' => 'RS-EXT-' . str_pad(count($createdRecords) + 1, 4, '0', STR_PAD_LEFT),
                'room_service_name' => $serviceNames[count($createdRecords) % count($serviceNames)] . ' ' . $detailNames[count($createdRecords) % count($detailNames)] . ' Extra Service',
                'parent_id' => $randomParent->id,
                'type' => 'ROOM_SERVICE',
                'created_by' => 1,
                'updated_by' => 1,
                'created_by_name' => 'System',
                'updated_by_name' => 'System',
            ]);
            
            $createdRecords[] = $record;
        }
        
        $this->command->info('AspakServiceRoom seeder completed. Total records: ' . count($createdRecords));
        $this->command->info('Groups: ' . AspakServiceRoom::where('type', 'GROUP')->count());
        $this->command->info('Room Services: ' . AspakServiceRoom::where('type', 'ROOM_SERVICE')->count());
    }
}